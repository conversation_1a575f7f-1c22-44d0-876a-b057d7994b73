from typing import List, Optional, Dict, Any
from pydantic import BaseModel


# Schema for the request body
class AgentRequest(BaseModel):
    prompt: str
    context: Optional[Dict[str, Any]] = None  # If you want to pass extra context


# Schema for the response
class AgentResponse(BaseModel):
    status: str
    response: str
    details: Optional[Dict[str, Any]] = None


# Organization schema for the user
class Org(BaseModel):
    id: int
    url: str
    icon: str
    logo: str
    nickname: str
    org_type: str
    name_legal: str
    org_has_sbtsk_type: bool


# Role schema for user
class Role(BaseModel):
    role_id: int
    role_code: str
    role_is_admin: bool
    role_is_on_field: bool


# Service schema for the services the user has access to
class Srvc(BaseModel):
    srvc_id: int
    srvc_code: str
    srvc_desc: str
    srvc_icon: str
    srvc_title: str


# Schema for static routes user has access to
class AccessRoute(BaseModel):
    menu_id: str
    rights_type: List[str]


# User details schema
class UserDetails(BaseModel):
    org: Org
    name: str
    uuid: str
    email: str
    roles: List[Role]
    mobile: str
    timezone: str
    auto_dep_bm: Optional[Any] = None
    config_data: Optional[Any] = None
    designation: Optional[Any] = None
    onfield_app_access: Optional[Any] = None
    access_static_routes: Optional[Any] = None
    can_see_dep_requests: Optional[Any] = None
    access_service_routes: Optional[Any] = None
    cannot_change_password: Optional[Any] = None
    enable_call_consumer_button: Optional[Any] = None
    can_see_consumer_phonenumber: Optional[Any] = None
    restrict_export_dump_requests: Optional[Any] = None
    cannot_delete_service_requests: Optional[Any] = None


# Request schema for the API endpoint
class UserContext(BaseModel):
    token: str
    isMobileApp: bool
    uuid: str
    hash: str
    user_details: UserDetails


# Request body schema for the API endpoint
class RequestBody(BaseModel):
    user_context: Optional[UserContext] = None
    prompt: str
    timeline: str
    is_new_chat: bool = False
    session_id: Optional[str] = None
