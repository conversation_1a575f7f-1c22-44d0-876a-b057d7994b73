# Python Byte-code & Compilation Artifacts
# ======================================
# __pycache__/
*.pyc
*.pyd
*.pyo
# C extensions
*.so

# Virtual Environment
# ===================
# Standard virtual environment directory
venv/

# Pip Cache
# =========
# pip-wheel-cache (can sometimes be created in project root)
.pip_rewrite/


# Operating System Specific
# =========================
.DS_Store # macOS specific ignore
Thumbs.db # Windows specific ignore
ehthumbs.db # Windows media cache
Desktop.ini # Windows folder customization
*.bak # Backup files


# Distribution & Build Artifacts
# ==============================
dist/
build/
*.egg-info/
.eggs/
sdist/
wheel/
*.tgz
*.zip

# Sensitive Information / Configuration
# ====================================
# VERY IMPORTANT: NEVER commit API keys, passwords, or sensitive config!
.env # Common for environment variables (e.g., used by python-dotenv)
.flaskenv # Flask specific environment
config.ini # Or similar config files that contain secrets
*.key
*.pem
secrets.py # Or any file explicitly holding secrets