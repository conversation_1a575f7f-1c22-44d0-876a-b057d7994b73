import json
from typing import Any, Optional, <PERSON><PERSON>


def generate_markdown(
    items: Any, html_element: str, total_count: Optional[int] = None
) -> str:
    """
    Converts a list of items into a markdown ordered list.
    If the items are dictionaries with 'display_code' and 'error' keys, format them accordingly.
    Otherwise, formats the items as plain text.

    Args:
        items (list): List of items to be converted into markdown.
        total_count (Optional[int]): The total count of items.

    Returns:
        str: Markdown formatted ordered list.
    """

    if html_element == "list":
        # Check if items are dictionaries with 'display_code' and 'error'
        if (
            items
            and isinstance(items[0], dict)
            and "display_code" in items[0]
            and "error" in items[0]
        ):
            # Format the list based on display_code and error
            markdown_content = "\n".join(
                [
                    f"{i + 1}. {item['display_code']} : {item['error'].replace('_', ' ')}"
                    for i, item in enumerate(items)
                ]
            )
        else:
            # Otherwise, treat items as plain text
            markdown_content = "\n".join(
                [f"{i + 1}. {item}" for i, item in enumerate(items)]
            )

    elif html_element == "text":
        markdown_content = "\n".join([str(item) for item in items])
    # If a remaining count is provided and it's greater than 0, add the "and X more ..." line
    if total_count:
        markdown_content += f"\n\nTotal data: {total_count}"

    print("helpers :: generate_ordered_markdown :: markdown", markdown_content)
    return markdown_content


def parse_llm_response_content(original_text: str) -> Tuple[str, str]:
    """
    Parses original text and extracts content based on different patterns.
    
    Args:
        original_text (str): The original text to parse
        
    Returns:
        Tuple[str, str]: A tuple containing (final_content, parse_type)
        - final_content: The extracted content to use
        - parse_type: Description of how the content was parsed
    """
    print("helpers :: parse_llm_response_content :: function start")
    print("helpers :: parse_llm_response_content :: original_text", original_text)
    
    # Initialize with original text as fallback
    final_content_for_json_array = original_text
    
    try:
        # Attempt to parse the original_text to see if it's already a JSON structure
        temp_parsed_content = json.loads(original_text)
        
        print(
            "helpers :: parse_llm_response_content :: temp parsed content",
            temp_parsed_content,
        )
        
        # Check if it matches the expected top-level structure:
        # A list containing a single dictionary with 'type' and 'content' keys.
        if (
            isinstance(temp_parsed_content, list)
            and len(temp_parsed_content) == 1
            and isinstance(temp_parsed_content[0], dict)
            and "type" in temp_parsed_content[0]
            and "content" in temp_parsed_content[0]
        ):
            # If it is, extract the inner 'content' string
            final_content_for_json_array = temp_parsed_content[0]["content"]
            print(
                "helpers :: parse_llm_response_content :: extracted inner content from target JSON structure"
            )
            return final_content_for_json_array, "target_json_structure"
        else:
            # It's a valid JSON, but not the specific structure we're looking for,
            # so treat the original_text as raw markdown.
            print(
                "helpers :: parse_llm_response_content :: valid JSON but not target structure, treating as raw markdown"
            )
            return final_content_for_json_array, "valid_json_not_target"
            
    except json.JSONDecodeError:
        # If original_text is not a valid JSON string, check if it follows array + text pattern
        print(
            "helpers :: parse_llm_response_content :: checking if original_text follows array + text pattern",
            original_text,
        )
        
        # --- Check for array + text pattern ---
        if (
            isinstance(original_text, str)
            and "[" in original_text
            and "]" in original_text
        ):
            print(
                "helpers :: parse_llm_response_content :: original_text contains array-like structure"
            )
            # Check if the string has an array-like structure followed by text
            # Extract the text after the array-like pattern
            try:
                # Extract the JSON-like part up until the first closing bracket
                json_end = (
                    original_text.rfind("]") + 1
                )  # Get up to the first closing bracket
                text_after_json = ""
                print(
                    "helpers :: parse_llm_response_content :: json part",
                    json_end,
                )
                
                if json_end == 0:
                    json_end = original_text.rfind("}") + 1
                
                if json_end > 0:
                    # Extract everything after the JSON structure
                    text_after_json = (
                        original_text[json_end:].strip()
                        if original_text[json_end:].strip()
                        else "Oops! Something went wrong while processing your request. Please try again in a moment. If the issue persists, consider starting a new session to help resolve the problem."
                    )
                
                print(
                    "helpers :: parse_llm_response_content :: text after json",
                    text_after_json,
                )
                final_content_for_json_array = (
                    text_after_json
                    if text_after_json
                    else "Oops! Something went wrong while processing your request. Please try again in a moment. If the issue persists, consider starting a new session to help resolve the problem."
                )
                
                return final_content_for_json_array, "array_plus_text_pattern"
                
            except Exception as e:
                print(
                    "helpers :: parse_llm_response_content :: error extracting array and text:",
                    e,
                )
                # Fall through to return original text
                pass
        else:
            print(
                "helpers :: parse_llm_response_content :: original_text does not contain array-like structure"
            )
    
    print("helpers :: parse_llm_response_content :: returning original text as final content")
    return final_content_for_json_array, "plain_text"
