import json
from fastapi import APIRouter, HTTPException
from app.services.brand_agent_service import (
    chat_with_brand_agent_logic,
)  # Import the service function
from app.services.srvc_prvdr_agent_service import chat_with_sp_agent_logic
from app.models.agent_model import RequestBody


router = APIRouter()


@router.post("/tms/agent/chat/sp")
async def chat_with_sp_agent(request_body: RequestBody):
    try:

        print("endpoints :: chat_with_sp_agent :: request_body", request_body)
        # Extract data from the request body
        prompt = request_body.prompt
        user_context = request_body.user_context
        is_new_chat = request_body.is_new_chat
        session_id = request_body.session_id
        # timeline = request_body.timeline

        # Handle the case where user_context is not provided
        if user_context is None:
            # You can decide on a default value or behavior when user_context is None
            user_context = {}

        # Call the business logic function from the service layer
        final_response_text, context, session_id = await chat_with_sp_agent_logic(
            prompt, user_context, is_new_chat, session_id
        )

        parsed_response_content = json.loads(final_response_text)

        return {
            "status": "success",
            "response": parsed_response_content,
            "details": context,
            "session_id": session_id,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Agent error: {str(e)}")


@router.post("/tms/agent/chat/brand")
async def chat_with_brand_agent(request_body: RequestBody):
    try:

        print("endpoints :: chat_with_brand_agent :: request_body", request_body)
        # Extract data from the request body
        prompt = request_body.prompt
        user_context = request_body.user_context
        is_new_chat = request_body.is_new_chat
        session_id = request_body.session_id
        # timeline = request_body.timeline

        # Handle the case where user_context is not provided
        if user_context is None:
            # You can decide on a default value or behavior when user_context is None
            user_context = {}

        # Call the business logic function from the service layer
        final_response_text, context, session_id = await chat_with_brand_agent_logic(
            prompt, user_context, is_new_chat, session_id
        )

        parsed_response_content = json.loads(final_response_text)

        print(
            "endpoints :: chat_with_brand_agent :: final response",
            {
                "status": "success",
                "response": parsed_response_content,
                "details": context,
                "session_id": session_id,
            },
        )

        return {
            "status": "success",
            "response": parsed_response_content,
            "details": context,
            "session_id": session_id,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Agent error: {str(e)}")
