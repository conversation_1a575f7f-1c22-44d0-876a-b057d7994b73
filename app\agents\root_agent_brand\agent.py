from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm
from app.agents.root_agent_brand.prompts import INSTRUCTION
from app.libs.openai import openai_models
from app.agents.root_agent_brand.tools.tools import (
    list_available_srvc_types,
    simple_after_model_modifier,
    simple_after_tool_modifier,
    list_available_statuses,
    list_srvc_requests_to_update,
    update_srvc_requests,
)


root_agent = LlmAgent(
    model=LiteLlm(model=openai_models["gpt4o"]),
    name="tms_brand_agent",
    description=(
        "Agent to help brands to bulk update the statuses of their service requests within the TMS(task management) system,"
    ),
    instruction=INSTRUCTION,
    tools=[
        list_available_srvc_types,
        list_available_statuses,
        list_srvc_requests_to_update,
        update_srvc_requests
    ],
    after_model_callback=simple_after_model_modifier,
    after_tool_callback=simple_after_tool_modifier,  
)
